<?php
/**
 * <AUTHOR> (<PERSON><PERSON> & <PERSON><PERSON><PERSON>)
 * @since 2.5.0
 * Enhanced Episode Design by DeshiFlix
 */

// Main data
$tmdb = get_post_meta($post->ID,'ids',true);
$ctrl = get_post_meta($post->ID,'clgnrt',true);
/*=====================================================*/
$query_seasons = DDbmoviesHelpers::GetAllSeasons($tmdb);
/*=====================================================*/

// Enhanced Episode Section Styles
?>
<style>
/* Enhanced Episode Section Styles */
#episodes.sbox {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 25px;
    margin: 20px 0;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.1);
}

#episodes h2 {
    color: white;
    text-align: center;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}

#seasons .se-c {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

#seasons .se-c:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

#seasons .se-c .se-q {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#seasons .se-c .se-q::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

#seasons .se-c .se-q:hover::before {
    left: 100%;
}

#seasons .se-c .se-q span.se-t {
    background: rgba(255,255,255,0.9);
    color: #333;
    padding: 12px 20px;
    border-radius: 50px;
    font-weight: 700;
    font-size: 18px;
    display: inline-block;
    margin-right: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    min-width: 60px;
    text-align: center;
}

#seasons .se-c .se-q span.title {
    color: white;
    font-size: 20px;
    font-weight: 600;
    text-shadow: 0 2px 8px rgba(0,0,0,0.3);
    vertical-align: middle;
}

/* Enhanced Episode List */
#seasons .se-c .se-a ul.episodios {
    padding: 0;
    margin: 0;
    list-style: none;
    background: #f8f9fa;
}

#seasons .se-c .se-a ul.episodios li {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    position: relative;
    background: white;
    margin-bottom: 2px;
}

#seasons .se-c .se-a ul.episodios li:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    transform: translateX(10px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

#seasons .se-c .se-a ul.episodios li:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

/* Enhanced Episode Number Badge */
#seasons .se-c .se-a ul.episodios li .numerando {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 12px 18px;
    border-radius: 25px;
    font-weight: 700;
    font-size: 14px;
    margin-right: 20px;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    min-width: 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
    letter-spacing: 1px;
}

#seasons .se-c .se-a ul.episodios li .numerando::before {
    content: 'EP';
    position: absolute;
    top: -2px;
    left: 8px;
    font-size: 9px;
    opacity: 0.7;
    font-weight: 500;
}

#seasons .se-c .se-a ul.episodios li .numerando::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

#seasons .se-c .se-a ul.episodios li:hover .numerando::after {
    left: 100%;
}

/* Enhanced Episode Image */
#seasons .se-c .se-a ul.episodios li .imagen {
    width: 120px;
    height: 68px;
    border-radius: 12px;
    overflow: hidden;
    margin-right: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: relative;
}

#seasons .se-c .se-a ul.episodios li .imagen img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

#seasons .se-c .se-a ul.episodios li:hover .imagen img {
    transform: scale(1.1);
}

#seasons .se-c .se-a ul.episodios li .imagen::after {
    content: '▶';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
    text-shadow: 0 2px 8px rgba(0,0,0,0.5);
}

#seasons .se-c .se-a ul.episodios li:hover .imagen::after {
    opacity: 1;
}

/* Enhanced Episode Title */
#seasons .se-c .se-a ul.episodios li .episodiotitle {
    flex: 1;
    padding-left: 0;
}

#seasons .se-c .se-a ul.episodios li .episodiotitle a {
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    display: block;
    margin-bottom: 5px;
    transition: color 0.3s ease;
    line-height: 1.4;
}

#seasons .se-c .se-a ul.episodios li .episodiotitle a:hover {
    color: #3498db;
}

#seasons .se-c .se-a ul.episodios li .episodiotitle span.date {
    color: #7f8c8d;
    font-size: 13px;
    font-weight: 500;
    background: rgba(127, 140, 141, 0.1);
    padding: 4px 12px;
    border-radius: 15px;
    display: inline-block;
}

/* Special Episodes */
#seasons .se-c .se-a ul.episodios li .numerando i.icon-star {
    color: #f39c12;
    margin-right: 5px;
}

/* Empty State */
#seasons .se-c .se-a ul.episodios li.none {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
    padding: 40px 20px;
    background: #f8f9fa;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    #episodes.sbox {
        padding: 15px;
        margin: 10px 0;
        border-radius: 15px;
    }

    #episodes h2 {
        font-size: 22px;
        margin-bottom: 20px;
    }

    #seasons .se-c .se-q {
        padding: 15px;
    }

    #seasons .se-c .se-q span.se-t {
        padding: 8px 15px;
        font-size: 16px;
        margin-right: 10px;
        min-width: 50px;
    }

    #seasons .se-c .se-q span.title {
        font-size: 16px;
    }

    #seasons .se-c .se-a ul.episodios li {
        flex-direction: column;
        align-items: flex-start;
        padding: 15px;
    }

    #seasons .se-c .se-a ul.episodios li .numerando {
        margin-right: 0;
        margin-bottom: 10px;
        align-self: flex-start;
    }

    #seasons .se-c .se-a ul.episodios li .imagen {
        width: 100%;
        height: 180px;
        margin-right: 0;
        margin-bottom: 15px;
    }

    #seasons .se-c .se-a ul.episodios li .episodiotitle {
        width: 100%;
    }
}

@media (max-width: 480px) {
    #seasons .se-c .se-a ul.episodios li .imagen {
        height: 150px;
    }

    #seasons .se-c .se-a ul.episodios li .numerando {
        padding: 8px 12px;
        font-size: 12px;
        min-width: 60px;
    }

    #seasons .se-c .se-a ul.episodios li .episodiotitle a {
        font-size: 14px;
    }
}
</style>

<?php
// Start Query
if($query_seasons && is_array($query_seasons) && $ctrl == true){
    $html_out = "<div id='episodes' class='sbox fixidtab'>";
    $html_out .="<h2><i class='fas fa-tv'></i> ".__d('Seasons and episodes')."</h2>";
    $html_out .="<div id='serie_contenido'><div id='seasons'>";
    $numb = 0;
    foreach($query_seasons as $season){
        $senumb = get_post_meta($season,'temporada', true);
        $aidate = get_post_meta($season,'air_date', true);
        $rating = get_post_meta($season,'_starstruck_avg', true);
        /*=====================================================*/
        $query_episodes = DDbmoviesHelpers::GetAllEpisodes($tmdb,$senumb);
        /*=====================================================*/
        $mnseo = $numb == 0 ? ' se-o' : false;
        $dsply = $numb == 0 ? ' style="display:block"' : false;
        $title = $senumb == '0' ? __d('Specials') : sprintf( __d('Season %s %s'), $senumb, '<i>'.doo_date_compose($aidate, false).'</i>');
        $nseas = $senumb == '0' ? '<i class="fas fa-star"></i>' : $senumb;
        // Continue View HTML
        $html_out .="<div class='se-c'>";
        $html_out .="<div class='se-q'>";
        $html_out .="<span class='se-t{$mnseo}'>{$nseas}</span>";
        $html_out .="<span class='title'>{$title}";
        if($rating >= '1'){
            $html_out .="<div class='se_rating'><i class='fas fa-star'></i> {$rating}</div>";
        }
        $html_out .="</span></div>";
        $html_out .="<div class='se-a'{$dsply}><ul class='episodios'>";
        if($query_episodes && is_array($query_episodes)){
            foreach($query_episodes as $episode){
                // Post Data
                $image = dbmovies_get_poster($episode,'dt_episode_a','dt_backdrop','w300');
                $name  = get_post_meta($episode,'episode_name', true);
                $episo = get_post_meta($episode,'episodio', true);
                $edate = get_post_meta($episode,'air_date', true);
                $edate = doo_date_compose($edate, false);
                $plink = get_permalink($episode);
                $title = !empty($name) ? $name : __('Episode').' '.$episo;
                // The View
                $html_out .= "<li class='mark-{$episo}'>";
                $html_out .= "<div class='imagen'><img src='{$image}' alt='Episode {$episo}'></div>";
                if($senumb !== '0'){
                    $html_out .= "<div class='numerando'>{$senumb}-{$episo}</div>";
                } else{
                    $html_out .= "<div class='numerando'><i class='icon-star'></i> {$episo}</div>";
                }
                $html_out .= "<div class='episodiotitle'><a href='{$plink}'>{$title}</a> <span class='date'><i class='fas fa-calendar'></i> {$edate}</span></div>";
                $html_out .= "</li>";
            }
        } else{
            $html_out .= "<li class='none'><i class='fas fa-info-circle'></i> ".__d('There are still no episodes this season')."</li>";
        }
        $html_out .="</ul></div></div>";
        $numb++;
    }
    $html_out .="</div></div></div>";
    echo apply_filters('dooplay_list_seasons_episodes', $html_out, $tmdb);
}
